/* Figtree font is now loaded from Google Fonts */

* {
  margin: 0;
  padding: 0;
  font-family: 'Figtree', sans-serif;
}




/* Navbar Container */
.navbar {
  padding: 0.75rem 0;
  transition: all 0.3s ease;
}

/* Logo Styles */
.navbar-brand img {
  transition: transform 0.3s ease;
}

.navbar-brand img:hover {
  transform: scale(1.05);
}

/* Toggle Button */
.navbar-toggler {
  border: none;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.navbar-toggler:focus {
  box-shadow: none;
  outline: none;
}

/* Navigation Links Container */
.bg-light-gradient {
  background: linear-gradient(to right, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.05));
  border-radius: 50px;
  padding: 0.25rem;
}

/* Individual Nav Links */
.nav-link {
  color: #939393 !important;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.5rem 1rem !important;
  margin: 0 0.25rem;
}

.nav-link:hover,
.nav-link.active {
  color: #563D39 !important;
  font-weight: 600;
}

/* <PERSON><PERSON> Styles */
.btn-primary {
  background-color: #563D39 !important;
  border-color: #563D39 !important;
  border-radius: 6px !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #4a332f !important;
  border-color: #4a332f !important;
  transform: translateY(-1px);
}

.btn-link {
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-link:hover {
  text-decoration: underline;
}

/* Mobile Styles (Below 992px) */
@media (max-width: 991.98px) {
  .navbar-collapse {
    background-color: white;
    padding: 1.5rem;
    margin-top: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }
  
  .bg-light-gradient {
    background: none !important;
    padding: 0 !important;
  }
  
  .nav-link {
    padding: 0.75rem 0 !important;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    margin: 0 !important;
  }
  
  .nav-link:last-child {
    border-bottom: none;
  }
  
  /* Mobile buttons container */
  .d-flex.d-lg-none.flex-column {
    padding: 1.5rem 0 0.5rem;
    margin-top: 1rem;
    border-top: 1px solid rgba(0,0,0,0.1);
    gap: 1rem !important;
  }
  
  /* Full width buttons on mobile */
  .navbar-nav,
  .d-flex.d-lg-none.flex-column .btn {
    width: 100%;
  }
}

/* Small Mobile Devices (Below 576px) */
@media (max-width: 575.98px) {
  .navbar-brand img {
    width: 100px;
  }
  
  .navbar-toggler img {
    width: 32px;
    height: 32px;
  }
  
  .navbar-collapse {
    padding: 1rem;
  }
}

/* Animation for dropdown */
.collapsing {
  transition: height 0.3s ease;
}

/* Ensure navbar stays on top */
.navbar.fixed-top {
  z-index: 1030;
}

/* Container adjustments for mobile */
@media (max-width: 767.98px) {
  .container {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
   .hero-content {
    align-items: center !important;
  }
}

/* Arrow icon in login button */
.btn-primary img {
  transition: transform 0.3s ease;
}

.btn-primary:hover img {
  transform: translateX(3px);
}