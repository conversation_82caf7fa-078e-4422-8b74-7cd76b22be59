<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Icon Paths</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .icon-test {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon-test img {
            width: 50px;
            height: 50px;
            border: 1px solid #ccc;
            display: block;
            margin-bottom: 5px;
        }
        .icon-test.error img {
            border-color: red;
        }
        .icon-test.success img {
            border-color: green;
        }
    </style>
</head>
<body>
    <h1>Testing Icon Paths</h1>
    <p>This page tests if the SVG icon paths are correct:</p>
    
    <div class="icon-test">
        <img src="assets/svg_icon/youtube-icon.svg" alt="YouTube" onerror="this.parentElement.classList.add('error')" onload="this.parentElement.classList.add('success')">
        <span>YouTube</span>
    </div>
    
    <div class="icon-test">
        <img src="assets/svg_icon/reddit_icon.svg" alt="Reddit" onerror="this.parentElement.classList.add('error')" onload="this.parentElement.classList.add('success')">
        <span>Reddit</span>
    </div>
    
    <div class="icon-test">
        <img src="assets/svg_icon/instagram.svg" alt="Instagram" onerror="this.parentElement.classList.add('error')" onload="this.parentElement.classList.add('success')">
        <span>Instagram</span>
    </div>
    
    <div class="icon-test">
        <img src="assets/svg_icon/facebook-icon.svg" alt="Facebook" onerror="this.parentElement.classList.add('error')" onload="this.parentElement.classList.add('success')">
        <span>Facebook</span>
    </div>
    
    <div class="icon-test">
        <img src="assets/svg_icon/twitter-icon.svg" alt="Twitter" onerror="this.parentElement.classList.add('error')" onload="this.parentElement.classList.add('success')">
        <span>Twitter</span>
    </div>
    
    <div class="icon-test">
        <img src="assets/svg_icon/pintrest-icon.svg" alt="Pinterest" onerror="this.parentElement.classList.add('error')" onload="this.parentElement.classList.add('success')">
        <span>Pinterest</span>
    </div>
    
    <div class="icon-test">
        <img src="assets/svg_icon/linkdin-icon.svg" alt="LinkedIn" onerror="this.parentElement.classList.add('error')" onload="this.parentElement.classList.add('success')">
        <span>LinkedIn</span>
    </div>
    
    <div class="icon-test">
        <img src="assets/svg_icon/thread.svg" alt="Threads" onerror="this.parentElement.classList.add('error')" onload="this.parentElement.classList.add('success')">
        <span>Threads</span>
    </div>
    
    <script>
        // Check if all images loaded successfully
        setTimeout(() => {
            const errorIcons = document.querySelectorAll('.icon-test.error');
            const successIcons = document.querySelectorAll('.icon-test.success');
            
            console.log(`Successfully loaded: ${successIcons.length} icons`);
            console.log(`Failed to load: ${errorIcons.length} icons`);
            
            if (errorIcons.length > 0) {
                console.log('Failed icons:');
                errorIcons.forEach(icon => {
                    const img = icon.querySelector('img');
                    console.log(`- ${img.alt}: ${img.src}`);
                });
            }
        }, 2000);
    </script>
</body>
</html>
